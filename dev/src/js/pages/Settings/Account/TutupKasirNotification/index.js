import React, { Fragment, useContext } from 'react';
import PropTypes from 'prop-types';
import {
    Box,
    Card,
    Heading,
    Text,
    Flex,
    InputSwitch,
    InputRadioGroup,
    InputRadio,
    Button,
    AlertDialog,
    ToastContext,
    FormGroup,
    FormLabel,
    Separator,
    InputSelect,
    FormHelper,
    InputTimePicker,
    Paragraph,
    InputNumber,
    IconButton,
    DevicePreview,
} from '@majoo-ui/react';
import { Controller, useFieldArray, useForm, useWatch } from 'react-hook-form';
import get from 'lodash/get';
import { connect } from 'react-redux';
import { ChevronLeftOutline, PlusOutline, TrashOutline } from '@majoo-ui/icons';
import { Trans, useTranslation } from 'react-i18next';
import CoreHOC from '../../../../core/CoreHOC';
import { defaultForm, resolver } from './form';
import { useTutupKasirNotification } from './hooks/useTutupKasirNotification';
import { TIMING_OPTION } from './enum';

const TutupKasirNotification = props => {
    const { branchList, router } = props;
    const toast = useContext(ToastContext);
    const { t } = useTranslation('Pengaturan/changeNotification');
    const forms = useForm({ defaultValues: defaultForm, resolver: resolver(t) });
    const {
        setValue,
        formState: { errors, isSubmitting },
        control,
    } = forms;
    const {
        append: appendRecipient,
        fields: recipientFields,
        remove: removeRecipient,
    } = useFieldArray({
        name: 'recipients',
        control,
    });
    const isTutupKasirNotif = useWatch({ control, name: 'is_tutup_kasir_notif' });

    const { loading, handleSetFinalForm, handleConfirm, show, setShow, whatsAppOptions } = useTutupKasirNotification({
        ...props,
        ...forms,
        toast,
        t,
    });

    return (
        <Card color="dark" responsive css={{ padding: 0 }}>
            <Box css={{ padding: '$spacing-05 $spacing-06' }}>
                <Button
                    css={{
                        paddingLeft: '0',
                        left: '-8px',
                        mb: '$spacing-03',
                        '&:hover': {
                            backgroundColor: 'inherit',
                            outline: 'none',
                        },
                        '> div': {
                            display: 'flex',
                        },
                    }}
                    onClick={() => router.goBack()}
                    buttonType="ghost"
                    leftIcon={<ChevronLeftOutline color="currentColor" />}
                >
                    {t('back')}
                </Button>
                <Heading heading="pageTitle">
                    {t('tutup_kasir_notif.form.title', 'Notifikasi Laporan Tutup Kasir')}
                </Heading>
            </Box>
            <Separator />
            <Box css={{ padding: '32px 24px', display: 'flex', flexDirection: 'column', gap: '$cozy' }}>
                <FormGroup responsive="input">
                    <FormLabel>
                        {t('tutup_kasir_notif.form.sendNotif.title', 'Kirim Notifikasi Laporan Tutup Kasir')}
                    </FormLabel>
                    <Controller
                        name="is_tutup_kasir_notif"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                            <InputSwitch
                                checked={value === '1'}
                                onCheckedChange={e => onChange(e ? '1' : '0')}
                                dataOnLabel="ON"
                                dataOffLabel="OFF"
                            />
                        )}
                    />
                </FormGroup>
                {isTutupKasirNotif === '1' && (
                    <Fragment>
                        <FormGroup responsive="input">
                            <Flex direction="column" gap={2}>
                                <FormLabel
                                    css={{
                                        '> span': {
                                            color: '$textPrimary',
                                            fontSize: '$sectionSubTitle',
                                            fontWeight: 600,
                                        },
                                    }}
                                >
                                    {t('tutup_kasir_notif.form.senderNumber.title')}
                                </FormLabel>
                                <Text variant="helper">{t('tutup_kasir_notif.form.senderNumber.description')}</Text>
                            </Flex>
                            <Controller
                                name="wa_sender_identifier"
                                control={control}
                                render={({ field: { value, onChange } }) => (
                                    <InputSelect
                                        value={value}
                                        option={whatsAppOptions}
                                        onChange={selectedValue => {
                                            onChange(selectedValue);
                                        }}
                                        placeholder={t('tutup_kasir_notif.form.senderNumber.placeholder')}
                                        css={{
                                            maxWidth: 300,
                                        }}
                                    />
                                )}
                            />
                            {get(errors, 'wa_sender_identifier') && (
                                <FormHelper variant="error">{get(errors, 'wa_sender_identifier.message')}</FormHelper>
                            )}
                        </FormGroup>
                        <Box>
                            <FormGroup responsive="input">
                                <FormLabel>{t('tutup_kasir_notif.form.timing.title')}</FormLabel>
                                <Controller
                                    name="timing_option"
                                    control={control}
                                    render={({ field: { value, onChange } }) => (
                                        <InputRadioGroup
                                            value={value}
                                            onValueChange={selectedValue => {
                                                onChange(selectedValue);
                                                if (selectedValue === TIMING_OPTION.IMMEDIATE) {
                                                    setValue('scheduled_time', '');
                                                }
                                            }}
                                        >
                                            <Flex direction="column" gap={6}>
                                                <InputRadio
                                                    value={TIMING_OPTION.IMMEDIATE}
                                                    label={
                                                        <Flex direction="column" gap={1} css={{ mt: -4 }}>
                                                            <Paragraph paragraph="longContentRegular" color="primary">
                                                                {t(
                                                                    'tutup_kasir_notif.form.timing.options.immediate.title',
                                                                )}
                                                            </Paragraph>
                                                            <Text variant="helper" color="secondary">
                                                                {t(
                                                                    'tutup_kasir_notif.form.timing.options.immediate.description',
                                                                )}
                                                            </Text>
                                                        </Flex>
                                                    }
                                                    css={{
                                                        '> div': {
                                                            alignItems: 'flex-start',
                                                        },
                                                    }}
                                                />
                                                <Flex>
                                                    <InputRadio
                                                        value={TIMING_OPTION.SCHEDULED}
                                                        css={{
                                                            '> div': {
                                                                alignItems: 'flex-start',
                                                            },
                                                        }}
                                                    />
                                                    <Flex direction="column" gap={1} css={{ mt: -12 }}>
                                                        <Flex align="center" gap={5}>
                                                            <Paragraph paragraph="longContentRegular" color="primary">
                                                                {t(
                                                                    'tutup_kasir_notif.form.timing.options.scheduled.title',
                                                                )}
                                                                :
                                                            </Paragraph>
                                                            <Controller
                                                                name="scheduled_time"
                                                                control={control}
                                                                render={({
                                                                    field: {
                                                                        value: scheduleValue,
                                                                        onChange: onScheduleChange,
                                                                    },
                                                                }) => (
                                                                    <InputTimePicker
                                                                        value={scheduleValue}
                                                                        onChange={selectedTime => {
                                                                            onScheduleChange(selectedTime);
                                                                        }}
                                                                        css={{
                                                                            maxWidth: 120,
                                                                        }}
                                                                    />
                                                                )}
                                                            />
                                                        </Flex>
                                                        <Text variant="helper" color="secondary">
                                                            {t(
                                                                'tutup_kasir_notif.form.timing.options.scheduled.description',
                                                            )}
                                                        </Text>
                                                    </Flex>
                                                </Flex>
                                            </Flex>
                                        </InputRadioGroup>
                                    )}
                                />
                                {get(errors, 'timing_option') && (
                                    <FormHelper variant="error">{get(errors, 'timing_option.message')}</FormHelper>
                                )}
                            </FormGroup>
                        </Box>
                        <FormGroup responsive="input">
                            <Flex direction="column" gap={2}>
                                <FormLabel
                                    css={{
                                        '> span': {
                                            color: '$textPrimary',
                                            fontSize: '$sectionSubTitle',
                                            fontWeight: 600,
                                        },
                                    }}
                                >
                                    {t('tutup_kasir_notif.form.recipients.title')}
                                </FormLabel>
                                <Text variant="helper">{t('tutup_kasir_notif.form.recipients.description')}</Text>
                            </Flex>
                            <Flex direction="column" gap={5} css={{ maxWidth: 332 }}>
                                {recipientFields.map((field, index) => (
                                    <Controller
                                        key={field.id}
                                        name={`recipients.${index}.value`}
                                        control={control}
                                        render={({ field: { value, onChange } }) => (
                                            <Flex align="center" gap={3}>
                                                <InputNumber
                                                    isInvalid={!!get(errors, `recipients.${index}.value`)}
                                                    value={value}
                                                    thousandSeparator={false}
                                                    prefix="+62"
                                                    maxLength={15}
                                                    allowEmptyFormatting
                                                    onValueChange={e => onChange(e.value)}
                                                />
                                                {index > 0 ? (
                                                    <IconButton onClick={() => removeRecipient(index)}>
                                                        <TrashOutline color="currentColor" />
                                                    </IconButton>
                                                ) : (
                                                    <Box css={{ size: 24 }} />
                                                )}
                                            </Flex>
                                        )}
                                    />
                                ))}

                                <Button
                                    buttonType="ghost"
                                    leftIcon={<PlusOutline color="currentColor" />}
                                    css={{ width: 160 }}
                                    disabled={recipientFields.length >= 10}
                                    onClick={() => appendRecipient({ value: '' })}
                                >
                                    {t('tutup_kasir_notif.form.recipients.addRecipient')}
                                </Button>
                            </Flex>
                            {get(errors, 'wa_sender_identifier') && (
                                <FormHelper variant="error">{get(errors, 'wa_sender_identifier.message')}</FormHelper>
                            )}
                        </FormGroup>
                        <FormGroup responsive="input">
                            <FormLabel>{t('tutup_kasir_notif.form.preview.title')}</FormLabel>
                            <DevicePreview
                                content={{
                                    sender: 'No Pengirim',
                                    message: () => (
                                        <span
                                            style={{ whiteSpace: 'pre-wrap' }}
                                            dangerouslySetInnerHTML={{ __html: templateContentWA }}
                                        />
                                    ),
                                }}
                                typeDevice="iphone"
                                variant="whatsapp"
                                size={200}
                            />
                        </FormGroup>
                    </Fragment>
                )}
                <Flex justify="end">
                    <Button
                        onClick={handleSetFinalForm}
                        css={{ mt: '30px' }}
                        disabled={!Array.isArray(branchList) || loading || isSubmitting}
                        buttonType="primary"
                    >
                        {t('save')}
                    </Button>
                </Flex>
            </Box>
            {show && (
                <AlertDialog
                    title={t('modalConfirmation.title')}
                    description={
                        <Trans
                            t={t}
                            i18nKey="modalConfirmation.description"
                            defaults="Apakah Anda yakin ingin menyimpan pengaturan ini?"
                        />
                    }
                    disabledButton={isSubmitting}
                    hideCloseButton={isSubmitting}
                    open={show}
                    onCancel={() => setShow(false)}
                    onConfirm={() => handleConfirm()}
                    labelCancel={t('modalConfirmation.cancel')}
                    labelConfirm={t('modalConfirmation.save')}
                />
            )}
        </Card>
    );
};

TutupKasirNotification.propTypes = {
    branchList: PropTypes.arrayOf(
        PropTypes.shape({
            id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
            name: PropTypes.string,
        }),
    ),
    router: PropTypes.shape({
        goBack: PropTypes.func,
    }),
};

TutupKasirNotification.defaultProps = {
    branchList: [],
    router: {
        goBack: () => {},
    },
};

const mapStateToProps = state => ({
    branchList: state.branch.list,
});

export default connect(mapStateToProps)(CoreHOC(TutupKasirNotification));
