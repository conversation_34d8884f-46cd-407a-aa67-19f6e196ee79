import { useEffect, useState, useMemo } from 'react';

export const useTutupKasirNotification = props => {
    const {
        filterBranch,
        hideProgress,
        idCabang,
        trigger,
        getValues,
        branchList,
        t,
    } = props;
    const [loading, setLoading] = useState(false);
    const [whatsAppOptions, setWhatsAppOptions] = useState([]);
    const [show, setShow] = useState(false);
    const outletId = filterBranch || idCabang;
    const outletName = useMemo(() => {
        if (outletId && branchList.length) return branchList.find(branch => branch.id_cabang === outletId).cabang_name;
        return '';
    }, [outletId, branchList]);

    const fetchSettings = async () => {
        try {
            setLoading(true);
        } catch (error) {
            console.error('Error fetching settings:', error);
        } finally {
            setLoading(false);
        }
    };

    const fetchWhatsAppList = async () => {};

    const messagePreview = useMemo(() => {
        const currentValues = getValues();
        const timingText =
            currentValues.tutup_kasir_setting?.timing_option === 'terjadwal'
                ? t('form.preview.scheduledMessage', { time: currentValues.tutup_kasir_setting?.scheduled_time || '' })
                : t('form.preview.immediateMessage');

        const baseMessage = t('form.preview.baseMessage', {
            outlet: outletName,
            timing: timingText,
        });

        return {
            text: baseMessage,
            whatsapp: baseMessage,
        };
    }, [getValues(), outletName, t]);

    const handleSetFinalForm = async () => {
        const isValid = await trigger();
        if (isValid) {
            setShow(true);
        }
    };

    const handleConfirm = async () => {};

    useEffect(async () => {
        if (outletId) {
            await fetchSettings();
            await fetchWhatsAppList();
        }
        hideProgress();
    }, [outletId]);

    return {
        loading,
        handleSetFinalForm,
        handleConfirm,
        show,
        setShow,
        outletName,
        whatsAppOptions,
        messagePreview,
    };
};
